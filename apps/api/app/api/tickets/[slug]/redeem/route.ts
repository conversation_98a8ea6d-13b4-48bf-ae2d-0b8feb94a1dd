import { withNextCors } from '@/app/lib/api';
import { apiAuth } from '@repo/auth/instances';
import { database, serializePrisma } from '@repo/database';
import { TicketStatus } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const postHandler = async (
  request: NextRequest,
  context: { params: Promise<{ slug: string }> }
) => {
  log.info('Ticket redemption request received');

  const { slug } = await context.params;

  const session = await apiAuth.api.getSession({
    headers: request.headers,
  });

  if (!slug) {
    return NextResponse.json(
      { error: 'Ticket slug is required' },
      { status: 400 }
    );
  }

  if (!session || !session.session.userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!session.session.activeOrganizationId) {
    return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
  }

  try {
    // Parse request body
    const body = await request.json().catch(() => ({}));
    const { type, remark } = body;

    // Validate redemption type
    if (!['entry', 'exit', 'invalid'].includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Invalid redemption type. Must be one of: entry, exit, invalid',
        },
        { status: 400 }
      );
    }

    // 1. Find the ticket by slug
    const ticket = await database.ticket.findUnique({
      where: {
        slug,
        AND: {
          event: {
            organizer: {
              user: {
                members: {
                  some: {
                    userId: session.user.id,
                    organizationId: session.session.activeOrganizationId,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json(
        {
          success: false,
          error: 'Ticket not found',
        },
        { status: 404 }
      );
    }

    // Check if ticket is in a valid state for redemption
    if (
      ticket.status !== TicketStatus.purchased &&
      ticket.status !== TicketStatus.reserved &&
      ticket.status !== TicketStatus.used
    ) {
      return NextResponse.json(
        {
          success: false,
          error: `Invalid ticket status: ${ticket.status}`,
        },
        { status: 400 }
      );
    }

    // 2 & 3. Update ticket status and create redemption record in a transaction
    const result = await database.$transaction(async (prisma) => {
      // Update ticket status to used
      const updatedTicket = await prisma.ticket.update({
        where: { id: ticket.id },
        data: {
          status: TicketStatus.used,
        },
        include: {
          timeSlot: true,
          order: true,
          event: {
            select: {
              title: true,
              organizer: {
                select: {
                  name: true,
                },
              },
            },
          },
          ticketType: {
            select: {
              name: true,
            },
          },
        },
      });

      // Create a new redemption record with the specified type and remark
      const redemption = await prisma.ticketRedemption.create({
        data: {
          ticketId: ticket.id,
          scannedBy: session.user.id,
          type,
          remark,
        },
      });

      return { updatedTicket, redemption };
    });

    let message = '';
    if (type === 'entry') {
      message = 'Ticket successfully admitted';
    } else if (type === 'exit') {
      message = 'Ticket successfully exited';
    } else if (type === 'invalid') {
      message = 'Ticket successfully marked as invalid';
    }

    return NextResponse.json(
      {
        success: true,
        message: message,
        data: serializePrisma(result.updatedTicket),
        redemption: serializePrisma(result.redemption),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error redeeming ticket:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to redeem ticket',
      },
      { status: 500 }
    );
  }
};

const getHandler = async (
  request: NextRequest,
  context: { params: Promise<{ slug: string }> }
) => {
  const { slug } = await context.params;

  const session = await apiAuth.api.getSession({
    headers: request.headers,
  });

  if (!slug) {
    return NextResponse.json(
      { error: 'Ticket slug is required' },
      { status: 400 }
    );
  }

  if (!session || !session.session.userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!session.session.activeOrganizationId) {
    return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
  }

  try {
    // 1. Find the ticket by slug
    const ticket = await database.ticket.findUnique({
      where: {
        slug,
        AND: {
          event: {
            organizer: {
              user: {
                members: {
                  some: {
                    userId: session.user.id,
                    organizationId: session.session.activeOrganizationId,
                  },
                },
              },
            },
          },
        },
      },
      select: {
        id: true,
      },
    });

    if (!ticket) {
      return NextResponse.json(
        {
          success: false,
          error: 'Ticket not found',
        },
        { status: 404 }
      );
    }

    // Fetch all redemptions for this ticket
    const redemptions = await database.ticketRedemption.findMany({
      where: {
        ticketId: ticket.id,
      },
      select: {
        id: true,
        type: true,
        ticketId: true,
        ticket: {
          select: {
            id: true,
            slug: true,
            ownerName: true,
            ownerEmail: true,
            ownerPhone: true,
            ticketTypeId: true,
            ticketType: {
              select: {
                name: true,
              },
            },
          },
        },
        user: {
          select: {
            name: true,
          },
        },
        remark: true,
        scannedAt: true,
        scannedBy: true,
      },
      orderBy: {
        scannedAt: 'desc',
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: serializePrisma(redemptions),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error fetching ticket scan history:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch ticket scan history',
      },
      { status: 500 }
    );
  }
};

export const OPTIONS = withNextCors<{ params: Promise<{ slug: string }> }>(
  getHandler
);
export const GET = withNextCors<{ params: Promise<{ slug: string }> }>(
  getHandler
);
export const POST = withNextCors<{ params: Promise<{ slug: string }> }>(
  postHandler
);
