'use client';

import { env } from '@/env';
import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import { useSession } from '@repo/auth/customer-client';
import { ModeToggle } from '@repo/design-system/components/mode-toggle';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON>etHeader,
  <PERSON>etT<PERSON><PERSON>,
  SheetTrigger,
} from '@repo/design-system/components/ui/sheet';
import { Menu } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export const EventHeader = () => {
  const { data: session } = useSession();
  const { user } = session || {};

  return (
    <header className="fixed top-0 right-0 left-0 z-40 w-full bg-background shadow-md">
      <div className="mx-auto flex min-h-16 max-w-4xl flex-row items-center justify-between max-lg:px-4">
        <Link href="/events">
          <Image
            src={darkLogo}
            alt="Logo"
            width={124}
            height={124}
            className="hidden dark:block"
          />
          <Image
            src={lightLogo}
            alt="Logo"
            width={124}
            height={124}
            className="block dark:hidden"
          />
        </Link>
        <div className="hidden md:flex items-center gap-2">
          <ModeToggle />
          <Button asChild size="sm" variant="link">
            <Link href="/account/orders">Find your tickets</Link>
          </Button>
          <Button asChild size="sm" variant="primary-red">
            {user ? (
              <Link href="/account">Profile</Link>
            ) : (
              <Link href="/auth/login">Sign up</Link>
            )}
          </Button>
        </div>

        <div className="flex items-center gap-2 md:hidden">
          <ModeToggle />
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="flex md:hidden">
                <Menu className="size-4" />
              </Button>
            </SheetTrigger>
            <SheetContent className="w-full h-full flex flex-col gap-0">
              <SheetHeader className="shadow-md">
                <SheetTitle>
                  <Link href="/events" className="block w-[40vw]">
                    <Image
                      src={darkLogo}
                      alt="Logo"
                      width={248}
                      height={124}
                      className="hidden dark:block"
                    />
                    <Image
                      src={lightLogo}
                      alt="Logo"
                      width={248}
                      height={124}
                      className="block dark:hidden"
                    />
                  </Link>
                </SheetTitle>
              </SheetHeader>
              <div className="flex flex-col items-center gap-3 justify-end p-4">
                <Button asChild variant="outline" size="lg" className="w-full">
                  <Link href="/events">Discover Events</Link>
                </Button>
                <Button
                  asChild
                  size="lg"
                  variant="primary-red"
                  className="w-full"
                >
                  <Link
                    href={new URL(
                      '/sign-up',
                      env.NEXT_PUBLIC_APP_URL
                    ).toString()}
                  >
                    Organize Events
                  </Link>
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};
