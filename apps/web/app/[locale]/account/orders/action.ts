'use server';

import { customerAuth } from '@repo/auth/instances';
import { database } from '@repo/database';
import { headers } from 'next/headers';

// Define a simplified order type for our use case
export type Order = {
  id: string;
  userId: string;
  eventId: string;
  orderedAt: Date;
  totalAmount?: number;
  paymentStatus?: string;
  event: {
    id: string;
    slug: string;
    name: string;
    description?: string;
    startDate?: Date;
    endDate?: Date;
    location?: string;
    imageUrl?: string;
  };
  tickets: {
    id: string;
    slug: string;
    orderId: string;
    ticketTypeId: string;
    ticketTypeName: string;
    status: string;
  }[];
};

export type GroupedOrders = {
  [eventName: string]: Order[];
};

export async function getOrders() {
  try {
    const session = await customerAuth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return { orders: [], groupedOrders: {} };
    }

    // Fetch orders with related data
    const dbOrders = await database.order.findMany({
      where: {
        userId: session.user.id,
        NOT: [
          {
            paymentStatus: 'cancelled',
          },
          {
            status: 'void',
          },
          {
            status: 'pending',
          },
        ],
      },
      include: {
        tickets: {
          include: {
            event: true,
            ticketType: true,
          },
        },
      },
      orderBy: {
        orderedAt: 'desc',
      },
    });

    // Map database orders to our Order type
    const orders: Order[] = [];

    for (const dbOrder of dbOrders) {
      // Skip orders with no tickets
      if (!dbOrder.tickets.length) continue;

      // Get event info from the first ticket (all tickets in an order should be for the same event)
      const firstTicket = dbOrder.tickets[0];
      const event = firstTicket.event;

      if (!event) continue;

      // Create our Order object
      const order: Order = {
        id: dbOrder.id,
        userId: dbOrder.userId,
        eventId: event.id,
        orderedAt: dbOrder.orderedAt,
        totalAmount: dbOrder.totalAmount?.toNumber(),
        paymentStatus: dbOrder.paymentStatus,
        event: {
          id: event.id,
          slug: event.slug,
          name: event.title || 'Unnamed Event',
          startDate: event.startTime || undefined,
          endDate: event.endTime || undefined,
          location: event.venueAddress || undefined,
          imageUrl: event.heroImageUrl || undefined, // No direct imageUrl in the schema
        },
        tickets: dbOrder.tickets.map((ticket) => ({
          id: ticket.id,
          slug: ticket.slug,
          orderId: ticket.orderId || ticket.id, // Fallback if orderId is null
          ticketTypeId: ticket.ticketTypeId,
          ticketTypeName: ticket.ticketType.name,
          status: ticket.status,
        })),
      };

      orders.push(order);
    }

    // Group orders by event name
    const groupedOrders = orders.reduce<GroupedOrders>((acc, order) => {
      const eventName = order.event.name;

      if (!acc[eventName]) {
        acc[eventName] = [];
      }

      acc[eventName].push(order);
      return acc;
    }, {});

    return { orders, groupedOrders };
  } catch (e) {
    console.error(e);
    return { orders: [], groupedOrders: {} };
  }
}

export async function getOrder(orderId: string) {
  try {
    const session = await customerAuth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return null;
    }

    const dbOrder = await database.order.findUnique({
      where: { id: orderId, userId: session.user.id },
      include: {
        event: true,
        tickets: {
          include: {
            event: true,
            ticketType: true,
          },
        },
      },
    });

    if (!dbOrder) {
      return null;
    }

    const event = dbOrder.event;

    if (!event) {
      return null;
    }

    const order: Order = {
      id: dbOrder.id,
      userId: dbOrder.userId,
      eventId: event.id,
      orderedAt: dbOrder.orderedAt,
      totalAmount: dbOrder.totalAmount?.toNumber(),
      paymentStatus: dbOrder.paymentStatus,
      event: {
        id: event.id,
        slug: event.slug,
        name: event.title || 'Unnamed Event',
        startDate: event.startTime || undefined,
        endDate: event.endTime || undefined,
        location: event.venueAddress || undefined,
        imageUrl: event.heroImageUrl || undefined, // No direct imageUrl in the schema
      },
      tickets: dbOrder.tickets.map((ticket) => ({
        id: ticket.id,
        slug: ticket.slug,
        orderId: ticket.orderId || ticket.id, // Fallback if orderId is null
        ticketTypeId: ticket.ticketTypeId,
        ticketTypeName: ticket.ticketType.name,
        status: ticket.status,
      })),
    };

    return order;
  } catch (e) {
    console.error(e);
    return null;
  }
}
