'use server';

import type { CheckoutFormValues } from '@/app/[locale]/(checkout)/checkout/components/checkout-form';
import { customerAuth } from '@repo/auth/instances';
import { database } from '@repo/database';
import { CartStatus, OrderStatus } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { headers } from 'next/headers';

/**
 * Updates the status of an expired cart
 * @param cartId The ID of the cart to update
 * @returns True if the update was successful, false otherwise
 */
export async function updateExpiredCartStatus(
  cartId: string
): Promise<boolean> {
  try {
    log.info('Updating expired cart status', { cartId });
    await database.cart.update({
      where: { id: cartId },
      data: { status: CartStatus.expired },
    });
    return true;
  } catch (error) {
    log.error('Failed to update expired cart status', {
      error,
      cartId,
    });
    return false;
  }
}

/**
 * Retrieves existing order information for a cart
 * @param cartId The ID of the cart to check for existing orders
 * @returns Object containing user info and order ID if found, null otherwise
 */
export async function getExistingOrderInfo(cartId: string): Promise<{
  existingUserInfo: Partial<CheckoutFormValues> | null;
  existingOrderId: string | null;
}> {
  let existingUserInfo: Partial<CheckoutFormValues> | null = null;
  let existingOrderId: string | null = null;

  try {
    // Find the most recent order in 'hold' status for this cart
    const existingOrder = await database.order.findFirst({
      where: {
        status: OrderStatus.hold,
        cartId,
      },
      orderBy: { createdAt: 'desc' },
      include: { user: true },
    });

    if (existingOrder?.user) {
      log.info('Found existing order in hold status for cart', {
        cartId,
        orderId: existingOrder.id,
        userId: existingOrder.userId,
      });

      // Extract user information to pre-fill the form
      existingUserInfo = {
        fullName: existingOrder.user.name || '',
        email: existingOrder.user.email || '',
        phone: existingOrder.user.phone || '',
        paymentMethod:
          (existingOrder.paymentMethod as
            | 'fpx'
            | 'card'
            | 'duitnow'
            | 'eWallet') || 'fpx',
      };
      existingOrderId = existingOrder.id;
    }
  } catch (error) {
    log.error('Error finding existing order for cart', { error, cartId });
    // Continue without existing user info if there's an error
  }

  // if no existing order found and user logged in, return user info only
  if (!existingOrderId) {
    const session = await customerAuth.api.getSession({
      headers: await headers(),
    });

    if (session) {
      existingUserInfo = {
        fullName: session.user.name || '',
        email: session.user.email || '',
        phone: session.user.phone || '',
      };
    }
  }

  return { existingUserInfo, existingOrderId };
}
