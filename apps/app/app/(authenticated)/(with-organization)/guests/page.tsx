import { organizerAuth } from '@repo/auth/instances';
import {} from '@repo/design-system/components/ui/breadcrumb';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { UserTable } from './components/user-table';

const title = 'TicketCARE - Users';
const description = 'TicketCARE - Users';

export const metadata: Metadata = {
  title,
  description,
};

const GuestsPage = async (): Promise<ReactElement> => {
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  return (
    <>
      <Header page="Users" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div>
          <div className="flex items-center justify-end" />
        </div>
        <div className="min-h-[100vh] flex-1 rounded-xl md:min-h-min">
          <UserTable />
        </div>
      </div>
    </>
  );
};

export default GuestsPage;
