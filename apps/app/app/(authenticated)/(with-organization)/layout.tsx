import { env } from '@/env';
import { StoreResetHandler } from '@repo/auth/components/onboarding/store-reset-handler';
import { organizerAuth } from '@repo/auth/instances';
import type { User } from '@repo/database/types';
import { SidebarProvider } from '@repo/design-system/components/ui/sidebar';
import { showBetaFeature } from '@repo/feature-flags';
import { NotificationsProvider } from '@repo/notifications/components/provider';
import { secure } from '@repo/security';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import type { ReactNode } from 'react';
import { OnboardingReminder } from './components/onboarding-reminder';
import { PostHogIdentifier } from './components/posthog-identifier';
import { GlobalSidebar } from './components/sidebar';
import { SWRProvider } from './components/swr';
import Script from 'next/script';

type AppLayoutProperties = {
  readonly children: ReactNode;
};

const AppLayout = async ({ children }: AppLayoutProperties) => {
  if (env.ARCJET_KEY) {
    await secure(['CATEGORY:PREVIEW']);
  }

  // Check if user is already authenticated
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    // If user is not authenticated, redirect to sign-in page
    return redirect('/sign-in');
  }

  // if (!session?.session.activeOrganizationId && !session?.session.organizerId) {
  //   // User needs to complete onboarding (if no organization and no organizer profile)
  //   return redirect('/onboarding');
  // }

  const betaFeature = await showBetaFeature();

  const user = session.user;
  const activeOrganizationName = session.session.activeOrganizationName;

  return (
    <SWRProvider>
      <NotificationsProvider userId={user.id}>
        <SidebarProvider>
          <GlobalSidebar
            user={user as User}
            activeOrganizationName={activeOrganizationName ?? undefined}
          >
            {betaFeature && (
              <div className="m-4 rounded-full bg-blue-500 p-1.5 text-center text-sm text-white">
                Beta feature now available
              </div>
            )}
            <OnboardingReminder />
            {children}
          </GlobalSidebar>
          <PostHogIdentifier />
          {/* This component checks and resets the store on page load if needed */}
          <StoreResetHandler />
          {/* Chat */}
          <Script id="thrivedesk-widget-init" strategy="beforeInteractive">
            {`!function(t,e,n){function s(){
  var t=e.getElementsByTagName("script")[0],n=e.createElement("script");
  n.type="text/javascript",n.async=!0,n.src="https://assistant.thrivedesk.com/bootloader.js?"+Date.now(),
  t.parentNode.insertBefore(n,t)}if(t.Assistant=n=function(e,n,s){t.Assistant.readyQueue.push({method:e,options:n,data:s})},
  n.readyQueue=[],"complete"===e.readyState)return s();
t.attachEvent?t.attachEvent("onload",s):t.addEventListener("load",s,!1)}
(window,document,window.Assistant||function(){}),window.Assistant("init","9fbb307c-9a70-4bc6-b13d-8a12304822ad");`}
          </Script>
        </SidebarProvider>
      </NotificationsProvider>
    </SWRProvider>
  );
};

export default AppLayout;
