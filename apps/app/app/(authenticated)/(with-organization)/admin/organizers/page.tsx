import { isSuperAdmin, toAuthResult } from '@repo/auth/permission-utils';
import { organizerAuth } from '@repo/auth/instances';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { Header } from '../../components/header';
import { getOrganizers } from './actions';
import { OrganizerDialog } from './components/organizer-dialog';
import { OrganizerTable } from './components/organizer-table';

export default async function OrganizersPage() {
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  // Check if user is a super admin
  if (!isSuperAdmin(toAuthResult(session))) {
    redirect('/');
  }

  const organizers = await getOrganizers();

  return (
    <>
      <Header page="Organizers" />

      <div className="flex items-center justify-between gap-4 px-4">
        <div>
          <h1 className="font-bold text-3xl">Organizers</h1>
          <p className="text-muted-foreground">
            Manage event organizers and their premium status
          </p>
        </div>
        <OrganizerDialog />
      </div>

      <div className="p-4">
        <OrganizerTable initialData={organizers} />
      </div>
    </>
  );
}
