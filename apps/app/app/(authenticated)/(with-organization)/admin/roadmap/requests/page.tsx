import { isSuperAdmin, toAuthResult } from '@repo/auth/permission-utils';
import { organizerAuth } from '@repo/auth/instances';
import { Button } from '@repo/design-system/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { Header } from '../../../components/header';
import { getPublicRequests } from '../actions';
import { RequestTable } from './components/request-table';

const title = 'TicketCARE - Requests';
const description = 'TicketCARE - Requests';

export const metadata: Metadata = {
  title,
  description,
};

export default async function RequestsPage() {
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  // Check if user is a super admin
  if (!isSuperAdmin(toAuthResult(session))) {
    redirect('/');
  }

  const requests = await getPublicRequests();

  return (
    <>
      <Header page="Requests" />

      <div className="flex items-center justify-between gap-4 px-4">
        <div>
          <h1 className="font-bold text-3xl">Public Requests</h1>
          <p className="text-muted-foreground">
            Review and manage feature requests submitted by users
          </p>
        </div>
        <Button asChild variant="outline">
          <Link href="/admin/roadmap">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Roadmap
          </Link>
        </Button>
      </div>

      <div className="p-4">
        <RequestTable initialData={requests} />
      </div>
    </>
  );
}
