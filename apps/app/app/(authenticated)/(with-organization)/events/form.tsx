'use client';

import type { SerializedEvent } from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSession } from '@repo/auth/organizer-client';
import { isSuperAdmin, toAuthResult } from '@repo/auth/permission-utils';
import type { Prisma } from '@repo/database/types';
import { Button } from '@repo/design-system/components/ui/button';
import { DatetimePicker } from '@repo/design-system/components/ui/datetime-picker';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  MultiSelector,
  MultiSelectorContent,
  MultiSelectorInput,
  MultiSelectorItem,
  MultiSelectorList,
  MultiSelectorTrigger,
} from '@repo/design-system/components/ui/multi-select';
import {
  RadioGroup,
  RadioGroupItem,
} from '@repo/design-system/components/ui/radio-group';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { generateSlugPrefix, slugPattern } from '@repo/design-system/lib/utils';
import { slugify } from '@repo/design-system/lib/utils';
import { useRouter } from 'next/navigation';
import { title } from 'radash';
import { useMemo, useState } from 'react';
import { z } from 'zod';
import { createEvent, updateEvent } from './actions';

interface EventFormProps {
  setOpen?: (open: boolean) => void;
  mode?: 'create' | 'edit';
  event?: SerializedEvent;
}

const eventCategories = [
  'arts',
  'auction',
  'charity',
  'competition',
  'concert',
  'conference',
  'corporate',
  'exhibition',
  'family',
  'fashion',
  'festival',
  'meeting',
  'religious',
  'social',
  'sports',
  'spiritual',
  'wellness',
  'workshop',
];

const eventTags = ['free', 'food', 'film', 'music', 'tech', 'virtual', '18+'];

const eventTypes = [
  {
    value: 'sales',
    label: 'Standard Ticketed Event',
    description:
      'Sell tickets to your event at a fixed price. Use this for concerts, conferences, workshops, and other events where you are selling admission.',
  },
  {
    value: 'ngo',
    label: 'Donation-Based Registration',
    description:
      'Attendees make a donation to secure their spot. Use this for fundraisers, galas, and charity events. "Buy Tickets" button will become "Reserve Seats".',
  },
];

const ticketSalesModes = [
  {
    value: 'pass_on_fee',
    label: 'Pass On Fee',
    description: 'Attendees pay the fees. You get the full ticket price.',
  },
  {
    value: 'absorb_fee',
    label: 'Absorb Fee',
    description: 'You pay the fees. Attendees see no extra costs.',
  },
];

// Define the Zod schema for event form validation
const eventFormSchema = z
  .object({
    title: z
      .string({ required_error: 'Title is required' })
      .min(3, { message: 'Title must be at least 3 characters' }),
    slug: z
      .string({ required_error: 'Slug is required' })
      .min(3, { message: 'Slug must be at least 3 characters' })
      .refine(
        (value) => {
          // Process the value through slugify and check if it's valid
          const processed = slugify(value);
          return processed.length >= 3 && slugPattern.test(processed);
        },
        {
          message:
            'Slug can only contain letters, numbers, hyphens, and Chinese characters',
        }
      )
      .transform((value) => {
        // Transform the value using slugify before submitting
        return slugify(value);
      }),
    startTime: z.date({ required_error: 'Start time is required' }),
    endTime: z.date({ required_error: 'End time is required' }),
    doorsOpen: z.date().optional(),
    category: z.array(z.string()).min(1, {
      message: 'Please select at least one category',
    }),
    tags: z.array(z.string()).optional(),
    eventType: z.enum(['sales', 'ngo']).default('sales'),
    ticketSalesMode: z
      .enum(['pass_on_fee', 'absorb_fee'])
      .default('pass_on_fee'),
    venueName: z.string().min(1, { message: 'Venue name is required' }),
    venueAddress: z.string().optional(),
  })
  .refine(
    (data) => {
      // Ensure end time is after start time
      return data.endTime > data.startTime;
    },
    {
      message: 'End time must be after start time',
      path: ['endTime'],
    }
  );

// Type for form values
type EventFormValues = z.infer<typeof eventFormSchema>;

export function EventForm({ setOpen, mode = 'create', event }: EventFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: session } = useSession();

  // Generate prefix once and memoize it
  const prefix = useMemo(() => {
    // If in edit mode and event has a slug, extract the prefix
    if (mode === 'edit' && event?.slug) {
      const parts = event.slug.split('-');
      if (parts.length > 0) {
        return parts[0];
      }
    }
    // Otherwise generate a new prefix
    return generateSlugPrefix('event');
  }, [mode, event?.slug]);

  // Initialize form with default values or existing event data
  const form = useForm<EventFormValues>({
    resolver: zodResolver(eventFormSchema),
    defaultValues:
      mode === 'edit'
        ? {
            title: event?.title || '',
            slug: event?.slug ? event.slug.split('-').slice(1).join('-') : '',
            startTime: event?.startTime
              ? new Date(event.startTime)
              : new Date(),
            endTime: event?.endTime ? new Date(event.endTime) : new Date(),
            doorsOpen: event?.doorsOpen ? new Date(event.doorsOpen) : undefined,
            category: event?.category || [],
            tags: event?.tags || [],
            eventType: (event?.eventType as 'sales' | 'ngo') || 'sales',
            ticketSalesMode: event?.ticketSalesMode,
            venueName: event?.venueName || '',
            venueAddress: event?.venueAddress || '',
          }
        : {
            startTime: (() => {
              const date = new Date();
              date.setDate(date.getDate() + 7);
              date.setHours(8, 0, 0, 0);
              return date;
            })(),
            endTime: (() => {
              const date = new Date();
              date.setDate(date.getDate() + 7);
              date.setHours(20, 0, 0, 0);
              return date;
            })(),
          },
  });

  // Handle form submission
  async function onSubmit(values: EventFormValues) {
    setIsSubmitting(true);

    try {
      // Create a safe copy of values and add the ticketSalesMode to the database fields
      const eventData = {
        ...values,
        // Ensure all required fields are present when creating/updating
      };

      // NOTE: hardcoded doors open for now
      eventData.doorsOpen = values.startTime;

      if (mode === 'create') {
        // combine slug before submit
        eventData.slug = `${prefix}-${values.slug}`;

        await createEvent(
          eventData as unknown as Prisma.EventUncheckedCreateInput
        );
        toast.success('Event created successfully');
      } else {
        if (!event) {
          throw new Error('No event to update');
        }
        // combine slug before submit
        eventData.slug = `${prefix}-${values.slug}`;

        const result = await updateEvent(
          event.id,
          eventData as unknown as Prisma.EventUncheckedCreateInput
        );
        toast.success('Event updated successfully');

        if (result.redirect) {
          router.replace(result.redirect);
        }
      }

      // Close the dialog if setOpen is provided
      if (setOpen) {
        setOpen(false);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';
      console.error('Failed to save event:', error);
      toast.error(`Failed to save event: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-3xl space-y-5 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Event Title" type="" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="slug"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Slug (Customize the final part of your URL.)
              </FormLabel>
              <FormControl>
                <div className="flex items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Input
                        value={prefix}
                        className="flex-1 cursor-not-allowed opacity-60"
                        autoFocus={false}
                        readOnly
                      />
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>This portion of the slug cannot be modified</p>
                    </TooltipContent>
                  </Tooltip>
                  -
                  <Input
                    placeholder="Slug"
                    type=""
                    pattern={slugPattern.source}
                    className="flex-4"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormDescription className="mt-1 text-muted-foreground text-xs">
                <span className="font-medium">Preview: </span>
                <span className="font-mono">
                  {process.env.NEXT_PUBLIC_WEB_URL ||
                    '[https://ticketcare.app](https://ticketcare.app)'}
                  /events/{prefix}-
                  {field.value ? slugify(field.value ?? '') : 'my-event-name'}
                </span>
              </FormDescription>

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="eventType"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Event Type</FormLabel>
              <FormControl>
                <RadioGroup
                  value={field.value}
                  onValueChange={field.onChange}
                  className="grid grid-cols-2"
                >
                  {eventTypes.map((type) => (
                    <div
                      key={type.value}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroupItem value={type.value} id={type.value} />
                      <div className="flex items-center gap-2">
                        <label
                          htmlFor={type.value}
                          className="cursor-pointer font-medium text-sm"
                        >
                          {type.label}
                        </label>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              className="text-muted-foreground hover:text-foreground"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="14"
                                height="14"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <title>Help</title>
                                <circle cx="12" cy="12" r="10" />
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                                <path d="m12 17h.01" />
                              </svg>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-xs">
                            <p>{type.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </FormControl>
              <FormDescription>
                Choose the event type to customize the user experience
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="ticketSalesMode"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Ticket Sales Mode</FormLabel>
              <FormControl>
                <RadioGroup
                  value={field.value}
                  onValueChange={field.onChange}
                  className="grid grid-cols-2"
                >
                  {ticketSalesModes.map((mode) => (
                    <div
                      key={mode.value}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroupItem value={mode.value} id={mode.value} />
                      <div className="flex items-center gap-2">
                        <label
                          htmlFor={mode.value}
                          className="cursor-pointer font-medium text-sm"
                        >
                          {mode.label}
                        </label>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              className="text-muted-foreground hover:text-foreground"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="14"
                                height="14"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <title>Help</title>
                                <circle cx="12" cy="12" r="10" />
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                                <path d="m12 17h.01" />
                              </svg>
                            </button>
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-xs">
                            <p>{mode.description}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              </FormControl>
              <FormDescription>
                Choose how processing fees are handled for ticket sales
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Category</FormLabel>
                <FormControl>
                  <MultiSelector
                    values={field.value ?? []}
                    onValuesChange={field.onChange}
                    loop
                    className="w-full"
                  >
                    <MultiSelectorTrigger>
                      <MultiSelectorInput placeholder="Select categories" />
                    </MultiSelectorTrigger>
                    <MultiSelectorContent>
                      <MultiSelectorList>
                        {eventCategories.map((category) => (
                          <MultiSelectorItem key={category} value={category}>
                            {title(category)}
                          </MultiSelectorItem>
                        ))}
                      </MultiSelectorList>
                    </MultiSelectorContent>
                  </MultiSelector>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* only show tags for super admin */}
          {isSuperAdmin(toAuthResult(session)) && (
            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <MultiSelector
                      values={field.value ?? []}
                      onValuesChange={field.onChange}
                      loop
                      className="w-full"
                    >
                      <MultiSelectorTrigger>
                        <MultiSelectorInput placeholder="Select tags" />
                      </MultiSelectorTrigger>
                      <MultiSelectorContent>
                        <MultiSelectorList>
                          {eventTags.map((tag) => (
                            <MultiSelectorItem key={tag} value={tag}>
                              {title(tag)}
                            </MultiSelectorItem>
                          ))}
                        </MultiSelectorList>
                      </MultiSelectorContent>
                    </MultiSelector>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="venueName"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Venue Name</FormLabel>
                <FormControl>
                  {/* <VenueAutocomplete {...field} value={String(field.value)} /> */}
                  <Textarea
                    placeholder="Venue Name"
                    className="resize-none"
                    {...field}
                    // Convert null to empty string
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="venueAddress"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Venue Address</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Venue Address"
                    className="resize-none"
                    {...field}
                    // Convert null to empty string
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="startTime"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Start Date</FormLabel>
                <DatetimePicker
                  {...field}
                  format={[
                    ['days', 'months', 'years'],
                    ['hours', 'minutes', 'am/pm'],
                  ]}
                />
                <FormDescription>When does the event start?</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="endTime"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>End Date</FormLabel>
                <DatetimePicker
                  {...field}
                  format={[
                    ['days', 'months', 'years'],
                    ['hours', 'minutes', 'am/pm'],
                  ]}
                />
                <FormDescription>When does the event end?</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" className="max-md:w-full" disabled={isSubmitting}>
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </Button>
      </form>
    </Form>
  );
}
