import { getOrganizerFilter, toAuthResult } from '@repo/auth/permission-utils';
import { organizerAuth } from '@repo/auth/instances';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import { Header } from '../components/header';
import { EventDialog } from './components/event-dialog';
import { PastEvents } from './past-events';
import { UpcomingEvents } from './upcoming-events';

const title = 'TicketCARE - Events';
const description = 'TicketCARE - Events';

export const metadata: Metadata = {
  title,
  description,
};

const EventPage = async () => {
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  // Use shared permission utility to derive organizer filter
  // - undefined: super admin (no filter)
  // - string: organizer's own ID
  // - null: non-organizer (no access) -> disable UI actions
  const organizerId = getOrganizerFilter(toAuthResult(session));

  return (
    <>
      <Header page="Events" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div>
          <Tabs defaultValue="upcoming">
            <div className="flex items-center justify-between">
              <TabsList>
                <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
                <TabsTrigger value="past">Past Events</TabsTrigger>
              </TabsList>
              <EventDialog
                mode={organizerId === null ? 'disabled' : 'create'}
              />
            </div>
            <TabsContent value="upcoming">
              <UpcomingEvents organizerId={organizerId} />
            </TabsContent>
            <TabsContent value="past">
              <PastEvents organizerId={organizerId} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  );
};

export default EventPage;
