'use server';

import { canAccessOrganizer, toAuthResult } from '@repo/auth/permission-utils';
import { organizerAuth } from '@repo/auth/instances';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

// Check the status of a premium upgrade session
export async function checkUpgradeStatus(sessionId: string) {
  try {
    // Find the premium upgrade session in the database
    const upgradeSession = await database.eventPremiumUpgrade.findUnique({
      where: { id: sessionId },
      select: {
        id: true,
        status: true,
        eventId: true,
        premiumTierId: true,
      },
    });

    if (!upgradeSession) {
      throw new Error('Upgrade session not found');
    }

    return serializePrisma(upgradeSession);
  } catch (error) {
    log.error('Error checking upgrade status', { error });
    throw new Error('Failed to check upgrade status');
  }
}

// Get upgrade information from session ID in search params
export async function getUpgradeInfoFromSession(
  slug: string,
  searchParams: { [key: string]: string | string[] | undefined }
) {
  try {
    const eventId = searchParams.eventId as string;
    const status = (searchParams.status as string) || 'success';

    if (!eventId) {
      throw new Error('Event Does Not Have Upgrade Session');
    }

    const session = await organizerAuth.api.getSession({
      headers: await headers(),
    });

    if (!session?.session?.organizerId) {
      throw new Error('Unauthorized: Only organizers can upgrade events');
    }

    // find latest 'pending' or 'failed' premium upgrade session of the same event event
    const upgradeSession = await database.eventPremiumUpgrade.findFirst({
      where: {
        eventId,
        event: {
          slug,
        },
        organizerId: session.session.organizerId,
        OR: [{ status: 'pending' }, { status: 'failed' }],
      },
      select: {
        id: true,
        status: true,
        eventId: true,
        premiumTierId: true,
        event: {
          select: {
            id: true,
            slug: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (!upgradeSession) {
      throw new Error('Upgrade session does not exist');
    }

    if (upgradeSession.event.slug !== decodeURIComponent(slug)) {
      // Check if the event slug matches
      throw new Error('Event slug mismatch');
    }

    if (!upgradeSession.premiumTierId) {
      throw new Error('Premium tier id missing');
    }

    // Determine the initial upgrade state based on the session status
    let initialUpgradeState = 'processing';
    if (upgradeSession.status === 'completed') {
      initialUpgradeState = 'confirmed';
    } else if (
      upgradeSession.status === 'cancelled' ||
      upgradeSession.status === 'failed'
    ) {
      initialUpgradeState = 'failed';
    }

    return {
      eventId: upgradeSession.eventId,
      premiumTierId: upgradeSession.premiumTierId,
      sessionId: upgradeSession.id,
      status: status,
      initialUpgradeState: initialUpgradeState,
    };
  } catch (error) {
    log.error('Error getting upgrade info', { error });
    notFound();
  }
}

// This function cleans up the related event premium upgrade rows
// When user payment failed > select different tier on next purchase
// New row will be created with new premium tier id since it is technically different upgrade
// This will mark previous premium upgrade rows as cancelled
export async function markInvalidUpgradeAsCancelled(
  eventId: string,
  slug: string
) {
  // Decode the slug for URL usage
  const decodedSlug = decodeURIComponent(slug);
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (!session?.session?.organizerId) {
    throw new Error('Unauthorized: Only organizers can upgrade events');
  }

  // Get the event to check if it belongs to the organizer
  const event = await database.event.findUnique({
    where: { id: eventId },
    select: {
      organizerId: true,
      isPremiumEvent: true,
    },
  });

  if (!event) {
    throw new Error('Event not found');
  }

  if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
    throw new Error('Unauthorized: You can only upgrade your own events');
  }

  // find rows with same event and organizer id with pending & failed status
  // and mark them as cancelled
  await database.eventPremiumUpgrade.updateMany({
    where: {
      eventId,
      organizerId: session.session.organizerId,
      paymentStatus: 'pending',
      OR: [{ status: 'pending' }, { status: 'failed' }],
    },
    data: {
      status: 'cancelled',
      paymentStatus: 'cancelled',
    },
  });

  revalidatePath('/events');
  revalidatePath(`/events/${decodedSlug}`);
}
