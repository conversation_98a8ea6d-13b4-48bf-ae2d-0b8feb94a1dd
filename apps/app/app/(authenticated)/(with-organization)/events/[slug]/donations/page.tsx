import { Header } from '@/app/(authenticated)/(with-organization)/components/header';
import { DonationColumns } from '@/app/(authenticated)/(with-organization)/events/[slug]/donations/components/donation-columns';
import { organizerAuth } from '@repo/auth/instances';
import { headers } from 'next/headers';
import { notFound, redirect } from 'next/navigation';
import { getEvent } from '../actions';
import { getEventDonations } from './action';
import { DonationTable } from './components/donation-table';

export default async function EventDonationsPage({
  params,
}: {
  params: { slug: string };
}) {
  const { slug } = await params;
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user || !session?.session.organizerId) {
    redirect('/');
  }

  const event = await getEvent(slug);

  if (!event) {
    notFound();
  }

  // Fetch initial data for the table
  const donations = await getEventDonations(event.id);

  return (
    <>
      <Header pages={['Events', event.slug]} page="Donations" />

      <div className="p-4">
        <h2 className="font-bold text-lg">Donations for {event.title}</h2>
        <DonationTable
          eventId={event.id}
          slug={slug}
          columns={DonationColumns}
          initialData={donations}
        />
      </div>
    </>
  );
}
