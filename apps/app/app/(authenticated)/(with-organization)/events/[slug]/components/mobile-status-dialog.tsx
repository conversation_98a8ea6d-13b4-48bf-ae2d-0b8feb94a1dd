'use client';

import { useIsPastEvent } from '@/app/hooks/use-is-past-event';
import type { SerializedEvent } from '@/types';
import { EventStatus } from '@prisma/client';
import { useSession } from '@repo/auth/organizer-client';
import { isSuper<PERSON>dmin, toAuthResult } from '@repo/auth/permission-utils';
import { Info, Loader2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { useState } from 'react';
import { updateEventStatus } from '../actions';
import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import { Button } from '@repo/design-system/components/ui/button';

interface MobileStatusDialogProps {
  event: SerializedEvent;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function MobileStatusDialog({
  event,
  open,
  onOpenChange,
}: MobileStatusDialogProps) {
  const isDesktop = useIsDesktop();
  const isPast = useIsPastEvent(event.startTime);
  const { data: session } = useSession();

  const [selectedStatus, setSelectedStatus] = useState<string>(
    isPast ? 'past' : event.status
  );
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleStatusChange = (newStatus: string) => {
    setSelectedStatus(newStatus);
    onOpenChange(false);
    setConfirmOpen(true);
  };

  const handleConfirm = async () => {
    setIsLoading(true);

    try {
      if (event.slug) {
        await updateEventStatus(event.slug, selectedStatus as EventStatus);

        const statusText = {
          draft: 'saved as draft',
          published: 'published',
          cancelled: 'cancelled',
          sold_out: 'marked as sold out',
        }[selectedStatus];

        toast.success(`Event ${statusText} successfully`);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to update event status. Please try again.';

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
      setConfirmOpen(false);
    }
  };

  return (
    <>
      {isDesktop ? (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Change Event Status</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Select the status for your event.
              </p>
              <Select
                value={selectedStatus}
                onValueChange={handleStatusChange}
                disabled={
                  isLoading || (isPast && !isSuperAdmin(toAuthResult(session)))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={EventStatus.draft}>Draft</SelectItem>
                  <SelectItem value={EventStatus.published}>
                    Published
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="ml-1 h-4 w-4 text-gray-500" />
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        You must add tickets for sale before publishing.
                      </TooltipContent>
                    </Tooltip>
                  </SelectItem>
                  <SelectItem value={EventStatus.cancelled}>
                    Cancelled
                  </SelectItem>
                  <SelectItem value={EventStatus.sold_out}>Sold Out</SelectItem>
                  {isPast && <SelectItem value="past">Past</SelectItem>}
                </SelectContent>
              </Select>
            </div>
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={onOpenChange}>
          <DrawerContent>
            <DrawerHeader className="text-left">
              <DialogTitle>Change Event Visibility</DialogTitle>
            </DrawerHeader>

            <div className="my-4 space-y-2 px-4">
              <p className="text-sm text-muted-foreground">
                Select the status for your event.
              </p>
              <Select
                value={selectedStatus}
                onValueChange={handleStatusChange}
                disabled={
                  isLoading || (isPast && !isSuperAdmin(toAuthResult(session)))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={EventStatus.draft}>Draft</SelectItem>
                  <SelectItem value={EventStatus.published}>
                    Published
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="ml-1 h-4 w-4 text-gray-500" />
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        You must add tickets for sale before publishing.
                      </TooltipContent>
                    </Tooltip>
                  </SelectItem>
                  <SelectItem value={EventStatus.cancelled}>
                    Cancelled
                  </SelectItem>
                  <SelectItem value={EventStatus.sold_out}>Sold Out</SelectItem>
                  {isPast && <SelectItem value="past">Past</SelectItem>}
                </SelectContent>
              </Select>
            </div>

            <DrawerFooter className="pt-2">
              <DrawerClose asChild>
                <Button variant="outline">Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      )}

      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Event Status</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the event status to{' '}
              {selectedStatus}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Continue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
