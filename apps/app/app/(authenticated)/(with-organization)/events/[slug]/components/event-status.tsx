'use client';

import { useIsPastEvent } from '@/app/hooks/use-is-past-event';
import type { SerializedEvent } from '@/types';
import { EventStatus } from '@prisma/client';
import { useSession } from '@repo/auth/organizer-client';
import { isSuperAdmin, toAuthResult } from '@repo/auth/permission-utils';
import { Info, Loader2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { useState } from 'react';
import { updateEventStatus } from '../actions';

interface EventStatusDialogProps {
  event: SerializedEvent;
}

export function EventStatusDialog({ event }: EventStatusDialogProps) {
  const isPast = useIsPastEvent(event.startTime);
  const { data: session } = useSession();

  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleStatusChange = (newStatus: string) => {
    setSelectedStatus(newStatus);
    setIsOpen(true);
  };

  const handleConfirm = async () => {
    if (selectedStatus) {
      setIsLoading(true);

      try {
        if (event.slug) {
          await updateEventStatus(event.slug, selectedStatus as EventStatus);

          // Show success message with appropriate status text
          const statusText = {
            draft: 'saved as draft',
            published: 'published',
            cancelled: 'cancelled',
            sold_out: 'marked as sold out',
          }[selectedStatus];

          toast.success(`Event ${statusText} successfully`);
        }
      } catch (error) {
        // Show error message
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to update event status. Please try again.';

        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    }
  };

  return (
    <>
      <Select
        value={isPast ? 'past' : event.status}
        onValueChange={handleStatusChange}
        disabled={isLoading || (isPast && !isSuperAdmin(toAuthResult(session)))}
      >
        <SelectTrigger className="w-[150px]">
          <SelectValue placeholder="Select status" />
          {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={EventStatus.draft}>Draft</SelectItem>
          <SelectItem value={EventStatus.published}>
            Published
            <Tooltip>
              <TooltipTrigger>
                <Info className="ml-1 h-4 w-4 text-gray-500" />
              </TooltipTrigger>
              <TooltipContent side="bottom">
                You must add tickets for sale before publishing.
              </TooltipContent>
            </Tooltip>
          </SelectItem>
          <SelectItem value={EventStatus.cancelled}>Cancelled</SelectItem>
          <SelectItem value={EventStatus.sold_out}>Sold Out</SelectItem>
          {isPast && <SelectItem value="past">Past</SelectItem>}
        </SelectContent>
      </Select>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Event Status</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the event status to{' '}
              {selectedStatus}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Continue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
