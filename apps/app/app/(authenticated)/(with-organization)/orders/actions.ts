'use server';

import { randomUUID } from 'node:crypto';
import { getOrganizerFilter, toAuthResult } from '@repo/auth/permission-utils';
import { organizerAuth } from '@repo/auth/instances';
import { database, generateTicketSlug, serializePrisma } from '@repo/database';
import {
  type Inventory,
  MAX_ROWS,
  type Prisma,
  TicketStatus,
  type TicketType,
} from '@repo/database/types';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import type { OrderFormValues } from './form';

interface SearchOrdersParams {
  search?: string;
  page?: number;
  pageSize?: number;
}

export async function getOrders() {
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  // Super admins can see all orders, regular organizers see only their own
  const organizerId = getOrganizerFilter(toAuthResult(session));

  const orders = await database.order
    .findMany({
      where: {
        ...(organizerId
          ? {
              tickets: {
                every: {
                  event: {
                    organizerId,
                  },
                },
              },
            }
          : {}),
      },
      select: {
        id: true,
        userId: true,
        eventId: true,
        event: true,
        status: true,
        totalAmount: true,
        paymentMethod: true,
        transactionId: true,
        paymentStatus: true,
        orderedAt: true,
        createdAt: true,
        updatedAt: true,
        user: true,
        tickets: {
          include: {
            event: true,
            ticketType: true,
            timeSlot: true,
          },
        },
        checkoutFormAnswer: true,
      },
      orderBy: {
        orderedAt: 'desc',
      },
      take: MAX_ROWS,
    })
    .then((orders) => serializePrisma(orders));

  return orders;
}

export async function searchOrders({
  search = '',
  page = 1,
  pageSize = 10,
}: SearchOrdersParams) {
  const skip = (page - 1) * pageSize;
  const numericSearch = Number.parseInt(search);

  const whereCondition: Prisma.OrderWhereInput = search
    ? {
        OR: [
          // Search by transaction ID
          ...(Number.isNaN(numericSearch)
            ? []
            : [
                {
                  id: String(numericSearch),
                },
              ]),
          // Search by name
          {
            user: {
              firstName: {
                contains: search,
                mode: 'insensitive',
              },
            },
          },
          // Search by name
          {
            user: {
              lastName: {
                contains: search,
                mode: 'insensitive',
              },
            },
          },
          // Search by email
          {
            user: {
              email: {
                contains: search,
                mode: 'insensitive',
              },
            },
          },
          // Search by event title
          // {
          //   tickets: {
          //     some: {
          //       event: {
          //         title: {
          //           contains: search,
          //           mode: "insensitive",
          //         },
          //       },
          //     },
          //   },
          // },
        ],
      }
    : {};

  const [orders, total] = await Promise.all([
    database.order.findMany({
      where: whereCondition,
      select: {
        id: true,
        status: true,
        paymentMethod: true,
        orderedAt: true,
        totalAmount: true,
        user: {
          select: {
            firstName: true,
          },
        },
        tickets: {
          select: {
            id: true,
            eventId: true,
            event: {
              select: {
                title: true,
                startTime: true,
                venue: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            ticketType: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        orderedAt: 'desc',
      },
      skip,
      take: pageSize,
    }),
    database.order.count({
      where: whereCondition,
    }),
  ]);

  return {
    orders: serializePrisma(orders),
    pagination: {
      total,
      pageCount: Math.ceil(total / pageSize),
      currentPage: page,
    },
  };
}

// Helper function to validate event, ticket type, and time slot
async function validateOrderData(data: OrderFormValues) {
  // Validate event exists
  const event = await database.event.findUnique({
    where: { id: data.eventId },
  });
  if (!event) {
    throw new Error('Event does not exist');
  }

  // fetch ticket type
  const ticketType = await database.ticketType.findUnique({
    where: { id: data.ticketTypeId },
  });
  if (!ticketType) {
    throw new Error('Ticket type does not exist');
  }

  // Validate quantity against min/max per order
  if (
    data.quantity < ticketType.minPerOrder ||
    data.quantity > ticketType.maxPerOrder
  ) {
    throw new Error(
      `Quantity must be between ${ticketType.minPerOrder} and ${ticketType.maxPerOrder}`
    );
  }

  // Validate time slot exists
  const timeSlot = await database.timeSlot.findUnique({
    where: { id: data.timeSlotId },
  });
  if (!timeSlot) {
    throw new Error('Time slot not found');
  }

  // Check inventory availability
  const inventory = await database.inventory.findFirst({
    where: {
      ticketTypeId: data.ticketTypeId,
      timeSlotId: data.timeSlotId,
    },
  });
  if (!inventory || inventory.quantity < data.quantity) {
    throw new Error('Not enough tickets available for this time slot');
  }

  return { ticketType, inventory };
}

// Helper function to get or create user
async function getOrCreateUser(data: OrderFormValues) {
  type UserData = Partial<
    Prisma.UserGetPayload<{
      select: {
        id: true;
        firstName: true;
        lastName: true;
        email: true;
        phone: true;
      };
    }>
  > | null;

  let user: UserData = null;

  if (data.userId === '-1') {
    // Creating a new user
    if (!data.customerName || !data.customerEmail) {
      throw new Error('Missing customer name/email');
    }

    const [firstName, ...lastNameParts] = data.customerName.split(' ');
    const lastName = lastNameParts.join(' ');

    // First, try to find an existing user by email
    user = await database.user.findFirst({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
      },
      where: {
        email: data.customerEmail,
      },
    });

    // If no user exists, create one
    if (!user && data.customerEmail && data.customerName) {
      user = await database.user.create({
        data: {
          id: randomUUID(),
          firstName,
          lastName: lastName || firstName,
          name: data.customerName,
          email: data.customerEmail,
          emailVerified: false,
          phone: data.customerPhone,
          dob: new Date('2000-01-01'),
        },
      });
    }

    if (!user) {
      throw new Error('Failed to create user');
    }
  } else {
    // Using existing user
    user = await database.user.findUnique({
      where: { id: data.userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
      },
    });

    if (!user) {
      throw new Error('User does not exist');
    }
  }

  return user;
}

// Define types for the transaction function parameters
type UserType = Partial<
  Prisma.UserGetPayload<{
    select: {
      id: true;
      firstName: true;
      lastName: true;
      email: true;
      phone: true;
    };
  }>
>;

// Helper function to create order and tickets in a transaction
async function createOrderTransaction(
  data: OrderFormValues,
  user: UserType,
  ticketType: TicketType,
  inventory: Inventory
) {
  return await database.$transaction(async (tx) => {
    // Create the order
    const order = await tx.order.create({
      data: {
        userId: user.id ?? data.userId,
        eventId: data.eventId,
        status: 'reserved',
        totalAmount: data.quantity * ticketType.price.toNumber(),
        paymentMethod: data.paymentMethod,
        transactionId: randomUUID(),
        paymentStatus: data.paymentStatus,
        orderedAt: new Date(),
      },
    });

    // Pre-generate unique slugs
    let slugs = Array.from({ length: data.quantity }).map(() =>
      generateTicketSlug()
    );
    // Ensure all slugs are unique (just in case)
    const uniqueSlugs = new Set(slugs);
    if (uniqueSlugs.size !== slugs.length) {
      // If we found duplicates, regenerate until all are unique
      slugs = Array.from(uniqueSlugs);
      while (slugs.length < data.quantity) {
        const newSlug = generateTicketSlug();
        if (!slugs.includes(newSlug)) {
          slugs.push(newSlug);
        }
      }
    }

    // Create tickets in bulk
    await tx.ticket.createMany({
      data: Array.from({ length: data.quantity }).map((_, index) => ({
        eventId: data.eventId,
        ticketTypeId: data.ticketTypeId,
        timeSlotId: data.timeSlotId,
        orderId: order.id,
        slug: slugs[index],
        status: TicketStatus.reserved,
        purchaseDate: new Date(),
        ownerName:
          user.firstName && user.lastName
            ? `${user.firstName} ${user.lastName}`
            : data.customerName || '',
        ownerEmail: user.email || data.customerEmail || '',
        ownerPhone: user.phone || data.customerPhone || '',
      })),
    });

    // Fetch the created tickets
    const createdTickets = await tx.ticket.findMany({
      where: { orderId: order.id },
    });

    // Update inventory quantity
    await tx.inventory.update({
      where: { id: inventory.id },
      data: { quantity: { decrement: data.quantity } },
    });

    return { order, tickets: createdTickets };
  });
}

export async function createOrder(data: OrderFormValues) {
  // Step 1: Validate order data
  const { ticketType, inventory } = await validateOrderData(data);

  // Step 2: Get or create user
  const user = await getOrCreateUser(data);

  // Step 3: Create order and tickets in a transaction
  const result = await createOrderTransaction(
    data,
    user,
    ticketType,
    inventory
  );

  // Step 4: Revalidate path and return result
  revalidatePath('/orders');

  return {
    success: true,
    data: {
      order: serializePrisma(result.order),
      tickets: result.tickets.map((ticket) => serializePrisma(ticket)),
    },
  };
}
