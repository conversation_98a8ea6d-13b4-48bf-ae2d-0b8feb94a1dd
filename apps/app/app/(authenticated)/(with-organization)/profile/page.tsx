import { getOrganizer } from '@/app/(authenticated)/(with-organization)/admin/organizers/actions';
import { OrganizerBiographyDialog } from '@/app/(authenticated)/(with-organization)/admin/organizers/components/organizer-biography-dialog';
import { OrganizerDialog } from '@/app/(authenticated)/(with-organization)/admin/organizers/components/organizer-dialog';
import { OrganizerLogoDialog } from '@/app/(authenticated)/(with-organization)/admin/organizers/components/organizer-logo-dialog';
import { Header } from '@/app/(authenticated)/(with-organization)/components/header';
import type { SerializedOrganizer } from '@/types';
import { organizerAuth } from '@repo/auth/instances';
import {
  ArrowLeft,
  ClipboardCheck,
  ImageIcon,
  Phone,
  StarIcon,
  User,
} from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Card<PERSON><PERSON><PERSON>,
} from '@repo/design-system/components/ui/card';
import { headers } from 'next/headers';
import Image from 'next/image';
import Link from 'next/link';
import { title } from 'radash';

// Use SerializedOrganizer as our Organizer type
type Organizer = SerializedOrganizer;

// Component for the organizer header section
function OrganizerHeader({
  organizer,
  isPremium = false,
}: { organizer: Organizer; isPremium?: boolean }) {
  return (
    <div className="relative flex flex-col space-y-2 md:flex-row md:items-center md:space-y-0">
      <div className="flex flex-1 items-center justify-between gap-4">
        <div>
          <h2 className="flex-1 font-bold text-3xl tracking-tight">
            Hello {organizer?.name}!
          </h2>
          <p className="text-muted-foreground">
            Manage your profile information
          </p>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button variant="outline" asChild>
          <Link href="/admin/organizers">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Link>
        </Button>
        {organizer && <OrganizerDialog mode="edit" organizer={organizer} />}
      </div>

      {isPremium && (
        <div className="-top-6 absolute left-0">
          <Badge variant="premium">
            <StarIcon className="h-3 w-3" />
            Premium
          </Badge>
        </div>
      )}
    </div>
  );
}

// Component for basic information card
function BasicInfoCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Basic Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-4">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Name</dt>
            <dd>{organizer?.name || 'N/A'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Slug</dt>
            <dd>{organizer?.slug || 'N/A'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Description
            </dt>
            <dd>{organizer?.description || 'N/A'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Status
            </dt>
            <dd>
              <Badge
                variant={
                  organizer?.verificationStatus.toLowerCase() as
                    | 'pending'
                    | 'verified'
                    | 'rejected'
                    | 'outline'
                }
              >
                {title(organizer?.verificationStatus ?? 'N/A')}
              </Badge>
            </dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

// Component for contact information card
function ContactInfoCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Phone className="h-5 w-5" />
          Contact Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-2">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Email</dt>
            <dd>{organizer?.email || 'N/A'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Phone</dt>
            <dd>{organizer?.phone || 'N/A'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Website
            </dt>
            <dd>{organizer?.website || 'N/A'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Address
            </dt>
            <dd>{organizer?.address || 'N/A'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Person in-charge
            </dt>
            <dd>
              {organizer?.picName ? (
                <>
                  {organizer?.picName}
                  {organizer?.picTitle && (
                    <span className="text-muted-foreground">
                      {' '}
                      ({organizer?.picTitle})
                    </span>
                  )}
                </>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

function SocialMediaCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Social Media
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-2 space-y-2">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              WhatsApp
            </dt>
            <dd>
              {organizer?.whatsapp ? (
                <a href={`https://wa.me/${organizer.whatsapp}`}>
                  {organizer.whatsapp}
                </a>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Facebook
            </dt>
            <dd>
              {organizer?.facebook ? (
                <a href={organizer.facebook}>{organizer.facebook}</a>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Instagram
            </dt>
            <dd>
              {organizer?.instagram ? (
                <a href={organizer.instagram}>{organizer.instagram}</a>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Twitter
            </dt>
            <dd>
              {organizer?.twitter ? (
                <a href={organizer.twitter}>{organizer.twitter}</a>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              YouTube
            </dt>
            <dd>
              {organizer?.youtube ? (
                <a href={organizer.youtube}>{organizer.youtube}</a>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              TikTok
            </dt>
            <dd>
              {organizer?.tiktok ? (
                <a href={organizer.tiktok}>{organizer.tiktok}</a>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              RedNote
            </dt>
            <dd>
              {organizer?.rednote ? (
                <a href={organizer.rednote}>{organizer.rednote}</a>
              ) : (
                'N/A'
              )}
            </dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

// Component for verification and payment card
function VerificationPaymentCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <ClipboardCheck className="h-5 w-5" />
          Verification & Payment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-2">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Payout Frequency
            </dt>
            <dd>
              {title(
                organizer?.payoutFrequency ? organizer?.payoutFrequency : 'N/A'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Commission Rate
            </dt>
            <dd>
              {organizer?.commissionRate
                ? `${organizer?.commissionRate}%`
                : 'N/A'}
            </dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

function OrganizerLogoCard({ organizer }: { organizer: Organizer }) {
  return (
    <div className="rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ImageIcon className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-semibold">Organization Logo</h3>
        </div>
        {organizer && <OrganizerLogoDialog organizer={organizer} />}
      </div>
      <h4 className="mt-1 text-left font-medium text-muted-foreground text-sm">
        Upload your organization logo to be displayed on your profile and
        tickets.
      </h4>
      <div className="mt-4">
        {organizer?.logo ? (
          <div className="flex justify-center">
            <div className="relative size-24 overflow-hidden rounded-md">
              <Image
                src={organizer?.logo}
                alt="Logo"
                fill
                className="object-cover"
              />
            </div>
          </div>
        ) : (
          <div className="flex h-24 w-full items-center justify-center rounded-md border border-dashed">
            <p className="text-muted-foreground text-sm">No logo available</p>
          </div>
        )}
      </div>
    </div>
  );
}

// Main component for rendering the organizer detail page
function OrganizerDetailContent({ organizer }: { organizer: Organizer }) {
  const isPremium = (organizer?.premiumUpgrades?.length ?? 0) > 0;

  return (
    <>
      <Header page="Profile" />

      <div className="flex-1 space-y-4 px-4">
        <OrganizerHeader organizer={organizer} isPremium={isPremium} />

        <div className="grid grid-cols-2 gap-4">
          <BasicInfoCard organizer={organizer} />
          <ContactInfoCard organizer={organizer} />
          <OrganizerBiographyDialog organizer={organizer} />
          <OrganizerLogoCard organizer={organizer} />
          <SocialMediaCard organizer={organizer} />
          {/* <VerificationPaymentCard organizer={organizer} /> */}
        </div>
      </div>
    </>
  );
}

// Main page component with reduced complexity
export default async function OrganizerSettingsPage() {
  const sessionData = await organizerAuth.api.getSession({
    headers: await headers(),
  });
  const { organizerId } = sessionData?.session ?? {};

  if (!organizerId) {
    return <OrganizerDetailContent organizer={null} />;
  }

  const organizer = await getOrganizer(organizerId);

  return <OrganizerDetailContent organizer={organizer} />;
}
