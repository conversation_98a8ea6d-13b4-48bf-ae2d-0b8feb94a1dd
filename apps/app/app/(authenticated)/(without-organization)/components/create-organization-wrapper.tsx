'use client';

import { organization } from '@repo/auth/organizer-client';
import { CreateOrganization } from '@repo/auth/components/create-organization';
import { log } from '@repo/observability/log';
import { useRouter } from 'next/navigation';

export function CreateOrganizationWrapper() {
  const router = useRouter();

  return (
    <CreateOrganization
      onSuccess={async (organizationData) => {
        try {
          // Get the list of organizations to find the newly created one
          const organizations = await organization.list();
          const newOrg = organizations.data?.find(
            (org) => org.slug === organizationData.slug
          );

          if (newOrg) {
            // Set the newly created organization as active
            await organization.setActive({
              organizationId: newOrg.id,
            });

            log.info('Set new organization as active:', {
              organizationId: newOrg.id,
            });
          }

          // Navigate to onboarding
          router.push('/onboarding');
        } catch (error) {
          log.warn('Failed to set active organization:', { error });
          // Fallback to hard refresh if setting active fails
          window.location.href = '/onboarding';
        }
      }}
      onError={(error) => {
        // Error handling is done in the component
        console.error('Failed to create organization:', error);
      }}
    />
  );
}
