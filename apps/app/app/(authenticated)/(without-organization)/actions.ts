'use server';

import { organizerAuth } from '@repo/auth/instances';
import type { OrganizerOnboardingData } from '@repo/auth/store/onboarding-store';
import { database } from '@repo/database';
import {
  PayoutFrequency,
  type Prisma,
  VerificationStatus,
} from '@repo/database/types';
import { generateSlugPrefix } from '@repo/design-system/lib/utils';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';

export async function completeOnboarding(
  formData: Partial<OrganizerOnboardingData>
) {
  // Get the current user session
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.id) {
    throw new Error('User not authenticated');
  }

  // Validate required fields - only name and email are needed now
  if (!formData.name || !formData.email) {
    throw new Error('Missing required fields: name and email are required');
  }

  // Create the organizer with default values for required fields
  try {
    // Onboarding form did not ask for slug to reduce onboarding friction
    // Generate a unique slug for the organizer profile using userId
    const slugPrefix = generateSlugPrefix('organizer');
    const uniqueSlug = `${slugPrefix}-${session.user.id.slice(-8)}`;

    const organizerData: Prisma.OrganizerCreateInput = {
      name: formData.name,
      slug: uniqueSlug,
      description: formData.description || null,
      website: formData.website || null,
      email: formData.email,
      phone: formData.phone || null,
      whatsapp: formData.whatsapp || null,
      facebook: formData.facebook || null,
      twitter: formData.twitter || null,
      instagram: formData.instagram || null,
      youtube: formData.youtube || null,
      tiktok: formData.tiktok || null,
      rednote: formData.rednote || null,
      picName: formData.picName || null,
      picTitle: formData.picTitle || null,
      address: formData.address || null,
      verificationStatus: VerificationStatus.pending,
      payoutFrequency: formData.payoutFrequency || PayoutFrequency.monthly,
      commissionRate: formData.commissionRate || 10,
      emailNotifications: formData.emailNotifications ?? true,
      smsNotifications: formData.smsNotifications ?? false,
      pushNotifications: formData.pushNotifications ?? false,
      user: {
        connect: { id: session.user.id },
      },
    };

    const organizer = await database.organizer.create({
      data: organizerData,
    });

    // Update the user's session with the new organizerId
    // Organization assignment is handled automatically during signup via better-auth hooks
    // New organizers are added as 'organizer' members; only newly created organizations should have an 'owner'.
    // Magic link is for customers only and does not create organizer membership.
    // Organizations are created manually for now.
    await database.session.updateMany({
      where: { userId: session.user.id },
      data: {
        organizerId: organizer.id,
      },
    });

    // Force refresh Better Auth cookie cache so the session cookie includes the new organizerId
    // Using nextCookies plugin, this will set the updated cookie in the response of this server action
    await organizerAuth.api.getSession({
      headers: await headers(),
      query: { disableCookieCache: true },
    });

    // Revalidate paths
    revalidatePath('/');
    revalidatePath('/admin');

    return {
      success: true,
      organizerId: organizer.id,
    };
  } catch (error) {
    console.error('Failed to complete organizer onboarding:', error);

    if (error instanceof Error) {
      throw new Error(error.message);
    }

    throw new Error('Failed to complete onboarding. Please try again.');
  }
}
