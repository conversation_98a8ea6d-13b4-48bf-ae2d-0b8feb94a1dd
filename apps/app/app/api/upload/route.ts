import { organizerAuth } from '@repo/auth/instances';
import { log } from '@repo/observability/log';
import { put } from '@repo/storage';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  log.info('File upload request received in app project');

  try {
    const session = await organizerAuth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'File is required' }, { status: 400 });
    }

    const access =
      (formData.get('access') as 'public' | 'private') || 'private';

    const result = await put(file.name, file, {
      access,
      contentType: file.type,
    });

    return NextResponse.json({
      success: true,
      url: result.url,
      pathname: result.pathname,
    });
  } catch (error) {
    console.error('Failed to upload file:', error);

    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

// Add OPTIONS handler for preflight requests
export async function OPTIONS() {
  return new NextResponse(null, { status: 204 });
}
