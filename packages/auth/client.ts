import {
  adminClient,
  inferAdditionalFields,
  magicLinkClient,
  organizationClient,
  emailOTPClient,
} from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import {
  adminRole,
  customerRole,
  organizerRole,
  ownerRole,
  superAdminRole,
} from './permissions';
import { ac } from './permissions';
import type { organizerAuth } from './instances';

/**
 * Base client configuration shared across all auth instances
 */
const baseClientConfig = {
  plugins: [
    inferAdditionalFields<typeof organizerAuth>(),
    magicLinkClient(),
    emailOTPClient(),
  ],
};

/**
 * Organizer-specific auth client
 * Used by app.ticketcare.my (organizer dashboard)
 */
const organizerAuthClient = createAuthClient({
  ...baseClientConfig,
  plugins: [
    ...baseClientConfig.plugins,
    adminClient({
      ac,
      roles: {
        superAdmin: superAdminRole,
        admin: adminRole,
        organizer: organizerRole,
      },
    }),
    organizationClient({
      ac,
      roles: { owner: owner<PERSON><PERSON>, organizer: organizer<PERSON><PERSON> },
    }),
  ],
});

/**
 * Customer-specific auth client
 * Used by www.ticketcare.my (customer portal)
 */
const customerAuthClient = createAuthClient({
  ...baseClientConfig,
  plugins: [
    ...baseClientConfig.plugins,
    adminClient({
      ac,
      roles: {
        customer: customerRole,
      },
    }),
    // Note: No organization client for customers
  ],
});

/**
 * API auth client
 * Used by api.ticketcare.my (can handle both organizer and customer)
 */
const apiAuthClient = createAuthClient({
  ...baseClientConfig,
  plugins: [
    ...baseClientConfig.plugins,
    adminClient({
      ac,
      roles: {
        superAdmin: superAdminRole,
        admin: adminRole,
        customer: customerRole,
        organizer: organizerRole,
      },
    }),
    organizationClient({
      ac,
      roles: { owner: ownerRole, organizer: organizerRole },
    }),
  ],
});

export { organizerAuthClient, customerAuthClient, apiAuthClient };
