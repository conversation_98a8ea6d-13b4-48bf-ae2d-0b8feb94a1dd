'use client';
import { useRouter } from 'next/navigation';
import { signOut as authSignOut } from '../api-client';
import { useOnboardingStore } from '../store/onboarding-store';

interface SignOutOptions {
  redirectTo?: string;
  fetchOptions?: {
    onSuccess?: () => void;
    onError?: (error: unknown) => void;
  };
}

/**
 * Flag to indicate that we're in the process of signing out
 * This helps prevent the onboarding reminder from showing during sign-out
 */
export const SIGN_OUT_IN_PROGRESS = 'sign_out_in_progress';

/**
 * Flag to indicate that we need to reset the store on next load
 * This is set during sign-out and checked on page load
 */
const RESET_STORE_ON_LOAD = 'reset_store_on_load';

/**
 * Function to check and reset the store if needed
 * This should be called on application initialization
 */
export const checkAndResetStore = () => {
  if (typeof window === 'undefined') {
    return;
  }

  // Check if we need to reset the store
  if (localStorage.getItem(RESET_STORE_ON_LOAD) === 'true') {
    // Reset the store
    const reset = useOnboardingStore.getState().reset;
    reset();

    // Clear the flags
    localStorage.removeItem(RESET_STORE_ON_LOAD);
    localStorage.removeItem(SIGN_OUT_IN_PROGRESS);
  }
};

/**
 * Custom sign-out handler that marks the store for reset after redirect
 * @param options Sign-out options including redirect URL and callbacks
 */
export const signOutWithReset = (options?: SignOutOptions) => {
  if (typeof window === 'undefined') {
    return authSignOut(options);
  }

  // Set flags in localStorage
  localStorage.setItem(SIGN_OUT_IN_PROGRESS, 'true');
  localStorage.setItem(RESET_STORE_ON_LOAD, 'true');

  // Create a new onSuccess callback
  const originalOnSuccess = options?.fetchOptions?.onSuccess;
  const newOptions = {
    ...options,
    fetchOptions: {
      ...options?.fetchOptions,
      onSuccess: () => {
        // Call the original onSuccess callback if provided
        if (originalOnSuccess) {
          originalOnSuccess();
        }
      },
    },
  };

  // Call the original signOut function with the new options
  return authSignOut(newOptions);
};

/**
 * Hook that provides a sign-out function that resets the onboarding store
 * @param redirectTo Optional URL to redirect to after sign-out (default: '/sign-in')
 * @returns A function that handles sign-out with store reset
 */
export const useSignOutWithReset = (redirectTo = '/sign-in') => {
  const router = useRouter();

  return () => {
    signOutWithReset({
      fetchOptions: {
        onSuccess: () => {
          // The reset will happen on the next page load
          router.push(redirectTo);
        },
      },
    });
  };
};
