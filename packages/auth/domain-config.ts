/**
 * Domain-based authentication strategy for separate auth servers
 *
 * Architecture:
 * - app.ticketcare.my (organizer auth) -> cookies: domain=.ticketcare.my, prefix=organizer-auth
 * - www.ticketcare.my (customer auth) -> cookies: domain=.ticketcare.my, prefix=customer-auth
 * - api.ticketcare.my (API server) -> cookies: domain=.ticketcare.my, prefix=api-auth
 *
 * Result: All share the same domain but use different cookie prefixes to prevent conflicts
 * API can read both organizer and customer cookies via validateAnySession()
 */

export type AuthServerType = 'organizer' | 'customer' | 'api';

/**
 * Get the cookie domain for a specific auth server
 * This ensures proper cookie isolation between organizer and customer domains
 */
export function getAuthServerCookieDomain(
  serverType: AuthServerType,
  host?: string
): string | undefined {
  if (process.env.NODE_ENV === 'development') {
    // In development, determine based on port or host
    if (host) {
      if (host.includes('localhost:3000') || host.includes('127.0.0.1:3000')) {
        return 'localhost'; // Organizer app
      }
      if (host.includes('localhost:3002') || host.includes('127.0.0.1:3002')) {
        return 'localhost'; // Customer app
      }
      if (host.includes('localhost:3001') || host.includes('127.0.0.1:3001')) {
        return 'localhost'; // API can access both
      }
    }
    return undefined; // No domain restriction for localhost fallback
  }

  // Production domain strategy
  switch (serverType) {
    case 'organizer':
      // Organizer auth server: share cookies between app.ticketcare.my and api.ticketcare.my
      // Use broader domain to allow API access while preventing www access via different cookie names
      return '.ticketcare.my';

    case 'customer':
      // Customer auth server: share cookies between www.ticketcare.my and api.ticketcare.my
      // Use broader domain to allow API access while preventing app access via different cookie names
      return '.ticketcare.my';

    case 'api':
      // API server: can read cookies from both app and www subdomains
      // Use the broader domain to access all subdomains
      return '.ticketcare.my';

    default:
      return '.ticketcare.my';
  }
}

/**
 * Determine which auth server type based on the current host
 */
export function determineAuthServerType(host?: string): AuthServerType {
  if (!host) {
    return 'api'; // Default fallback
  }

  // Development detection
  if (host.includes('localhost:3000') || host.includes('127.0.0.1:3000')) {
    return 'organizer';
  }
  if (host.includes('localhost:3002') || host.includes('127.0.0.1:3002')) {
    return 'customer';
  }
  if (host.includes('localhost:3001') || host.includes('127.0.0.1:3001')) {
    return 'api';
  }

  // Production detection
  if (host.includes('app.ticketcare.my')) {
    return 'organizer';
  }
  if (host.includes('www.ticketcare.my')) {
    return 'customer';
  }
  if (host.includes('api.ticketcare.my')) {
    return 'api';
  }

  // Default to API for unknown hosts
  return 'api';
}

/**
 * Get trusted origins for a specific auth server type
 */
export function getTrustedOriginsForAuthServer(
  serverType: AuthServerType
): string[] {
  const commonOrigins = [
    'ticketcare://', // Expo app scheme
    'exp://', // Expo development scheme
    'exp+ticketcare://', // Expo development scheme with app name
  ];

  switch (serverType) {
    case 'organizer':
      return [
        ...commonOrigins,
        'http://localhost:3000', // Organizer app dev
        'http://localhost:3001', // API dev
        'http://127.0.0.1:3000', // Organizer app dev alternative
        'http://127.0.0.1:3001', // API dev alternative
        'https://app.ticketcare.my', // Organizer app prod
        'https://api.ticketcare.my', // API prod
        'https://ticketcare-app.vercel.app', // Organizer app staging
        'https://ticketcare-api.vercel.app', // API staging
      ];

    case 'customer':
      return [
        ...commonOrigins,
        'http://localhost:3002', // Customer app dev
        'http://localhost:3001', // API dev
        'http://127.0.0.1:3002', // Customer app dev alternative
        'http://127.0.0.1:3001', // API dev alternative
        'https://www.ticketcare.my', // Customer app prod
        'https://ticketcare.my', // Customer app prod alternative
        'https://api.ticketcare.my', // API prod
        'https://ticketcare-web.vercel.app', // Customer app staging
        'https://ticketcare-api.vercel.app', // API staging
      ];

    case 'api':
      return [
        ...commonOrigins,
        'http://localhost:3000', // Organizer app dev
        'http://localhost:3001', // API dev
        'http://localhost:3002', // Customer app dev
        'http://127.0.0.1:3000', // Organizer app dev alternative
        'http://127.0.0.1:3001', // API dev alternative
        'http://127.0.0.1:3002', // Customer app dev alternative
        'https://app.ticketcare.my', // Organizer app prod
        'https://www.ticketcare.my', // Customer app prod
        'https://ticketcare.my', // Customer app prod alternative
        'https://api.ticketcare.my', // API prod
        'https://ticketcare-app.vercel.app', // Organizer app staging
        'https://ticketcare-web.vercel.app', // Customer app staging
        'https://ticketcare-api.vercel.app', // API staging
      ];

    default:
      return commonOrigins;
  }
}

/**
 * Cookie naming strategy for different auth instances
 * Since all instances now share the same domain (.ticketcare.my), they use different prefixes
 */
export function getCookieNamingStrategy() {
  return {
    // Organizer auth cookies (used by app.ticketcare.my)
    organizer: {
      sessionToken: 'organizer-auth.session_token',
      sessionData: 'organizer-auth.session_data',
      secureSessionToken: '__Secure-organizer-auth.session_token',
    },
    // Customer auth cookies (used by www.ticketcare.my)
    customer: {
      sessionToken: 'customer-auth.session_token',
      sessionData: 'customer-auth.session_data',
      secureSessionToken: '__Secure-customer-auth.session_token',
    },
    // API auth cookies (used by api.ticketcare.my)
    api: {
      sessionToken: 'api-auth.session_token',
      sessionData: 'api-auth.session_data',
      secureSessionToken: '__Secure-api-auth.session_token',
    },
  };
}
