/**
 * Domain-based authentication strategy for separate auth servers
 *
 * Architecture:
 * - app.ticketcare.my (organizer auth) -> cookies: app.ticketcare.my + api.ticketcare.my
 * - www.ticketcare.my (customer auth) -> cookies: www.ticketcare.my + api.ticketcare.my
 * - api.ticketcare.my (API server) -> can read both app and www cookies
 *
 * Result: app and www cannot share cookies with each other, only with API
 */

export type AuthServerType = 'organizer' | 'customer' | 'api';

/**
 * Get the cookie domain for a specific auth server
 * This ensures proper cookie isolation between organizer and customer domains
 */
export function getAuthServerCookieDomain(
  serverType: AuthServerType,
  host?: string
): string | undefined {
  if (process.env.NODE_ENV === 'development') {
    // In development, determine based on port or host
    if (host) {
      if (host.includes('localhost:3000') || host.includes('127.0.0.1:3000')) {
        return 'localhost'; // Organizer app
      }
      if (host.includes('localhost:3002') || host.includes('127.0.0.1:3002')) {
        return 'localhost'; // Customer app
      }
      if (host.includes('localhost:3001') || host.includes('127.0.0.1:3001')) {
        return 'localhost'; // API can access both
      }
    }
    return undefined; // No domain restriction for localhost fallback
  }

  // Production domain strategy
  switch (serverType) {
    case 'organizer':
      // Organizer auth server: share cookies only between app.ticketcare.my and api.ticketcare.my
      // Use a more specific domain to prevent sharing with www
      return 'app.ticketcare.my';

    case 'customer':
      // Customer auth server: share cookies only between www.ticketcare.my and api.ticketcare.my
      // Use a more specific domain to prevent sharing with app
      return 'www.ticketcare.my';

    case 'api':
      // API server: can read cookies from both app and www subdomains
      // Use the broader domain to access all subdomains
      return '.ticketcare.my';

    default:
      return '.ticketcare.my';
  }
}

/**
 * Determine which auth server type based on the current host
 */
export function determineAuthServerType(host?: string): AuthServerType {
  if (!host) {
    return 'api'; // Default fallback
  }

  // Development detection
  if (host.includes('localhost:3000') || host.includes('127.0.0.1:3000')) {
    return 'organizer';
  }
  if (host.includes('localhost:3002') || host.includes('127.0.0.1:3002')) {
    return 'customer';
  }
  if (host.includes('localhost:3001') || host.includes('127.0.0.1:3001')) {
    return 'api';
  }

  // Production detection
  if (host.includes('app.ticketcare.my')) {
    return 'organizer';
  }
  if (host.includes('www.ticketcare.my')) {
    return 'customer';
  }
  if (host.includes('api.ticketcare.my')) {
    return 'api';
  }

  // Default to API for unknown hosts
  return 'api';
}

/**
 * Get trusted origins for a specific auth server type
 */
export function getTrustedOriginsForAuthServer(
  serverType: AuthServerType
): string[] {
  const commonOrigins = [
    'ticketcare://', // Expo app scheme
    'exp://', // Expo development scheme
    'exp+ticketcare://', // Expo development scheme with app name
  ];

  switch (serverType) {
    case 'organizer':
      return [
        ...commonOrigins,
        'http://localhost:3000', // Organizer app dev
        'http://localhost:3001', // API dev
        'http://127.0.0.1:3000', // Organizer app dev alternative
        'http://127.0.0.1:3001', // API dev alternative
        'https://app.ticketcare.my', // Organizer app prod
        'https://api.ticketcare.my', // API prod
        'https://ticketcare-app.vercel.app', // Organizer app staging
        'https://ticketcare-api.vercel.app', // API staging
      ];

    case 'customer':
      return [
        ...commonOrigins,
        'http://localhost:3002', // Customer app dev
        'http://localhost:3001', // API dev
        'http://127.0.0.1:3002', // Customer app dev alternative
        'http://127.0.0.1:3001', // API dev alternative
        'https://www.ticketcare.my', // Customer app prod
        'https://ticketcare.my', // Customer app prod alternative
        'https://api.ticketcare.my', // API prod
        'https://ticketcare-web.vercel.app', // Customer app staging
        'https://ticketcare-api.vercel.app', // API staging
      ];

    case 'api':
      return [
        ...commonOrigins,
        'http://localhost:3000', // Organizer app dev
        'http://localhost:3001', // API dev
        'http://localhost:3002', // Customer app dev
        'http://127.0.0.1:3000', // Organizer app dev alternative
        'http://127.0.0.1:3001', // API dev alternative
        'http://127.0.0.1:3002', // Customer app dev alternative
        'https://app.ticketcare.my', // Organizer app prod
        'https://www.ticketcare.my', // Customer app prod
        'https://ticketcare.my', // Customer app prod alternative
        'https://api.ticketcare.my', // API prod
        'https://ticketcare-app.vercel.app', // Organizer app staging
        'https://ticketcare-web.vercel.app', // Customer app staging
        'https://ticketcare-api.vercel.app', // API staging
      ];

    default:
      return commonOrigins;
  }
}

/**
 * Configuration for API server to read cookies from multiple domains
 * Since API needs to validate sessions from both organizer and customer apps
 */
export function getApiCookieReadingStrategy() {
  return {
    // Try to read organizer session first
    organizerCookieDomain: 'app.ticketcare.my',
    // Then try customer session
    customerCookieDomain: 'www.ticketcare.my',
    // Cookie names to look for
    sessionCookieNames: [
      'better-auth.session_token',
      '__Secure-better-auth.session_token', // Secure cookie variant
    ],
  };
}
