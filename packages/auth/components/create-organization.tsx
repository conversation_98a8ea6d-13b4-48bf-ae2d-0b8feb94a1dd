'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { log } from '@repo/observability/log';
import type { ChangeEvent } from 'react';
import { useState } from 'react';
import { z } from 'zod';
import { organization } from '../api-client';

const formSchema = z.object({
  name: z.string().min(2, {
    message: 'Organization name must be at least 2 characters.',
  }),
  slug: z
    .string()
    .min(2, {
      message: 'Slug must be at least 2 characters.',
    })
    .regex(/^[a-z0-9-]+$/, {
      message: 'Slug can only contain lowercase letters, numbers, and hyphens.',
    }),
});

interface CreateOrganizationProps {
  onSuccess?: (organizationData: { name: string; slug: string }) => void;
  onError?: (error: unknown) => void;
}

export const CreateOrganization = ({
  onSuccess,
  onError,
}: CreateOrganizationProps) => {
  const [pending, setPending] = useState(false);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      slug: '',
    },
  });

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
  };

  // Track if the user has manually edited the slug
  const [slugManuallyEdited, setSlugManuallyEdited] = useState(false);

  const onNameChange = (e: ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    form.setValue('name', name);

    // Only auto-generate slug if user hasn't manually edited it
    if (!slugManuallyEdited) {
      const slug = generateSlug(name);
      form.setValue('slug', slug);

      // If slug is valid, check availability
      if (slug.length >= 2) {
        checkSlugAvailability(slug);
      } else {
        setSlugAvailable(null);
      }
    }
  };

  const checkSlugAvailability = async (slug: string) => {
    try {
      const result = await organization.checkSlug({ slug });
      setSlugAvailable(result.data?.status === true);
      if (result.data?.status === true) {
        form.clearErrors('slug');
      } else {
        form.setError('slug', { message: 'This slug is already taken.' });
      }
    } catch (error) {
      log.warn('check slug', { error });
    }
  };

  const onSlugChange = (e: ChangeEvent<HTMLInputElement>) => {
    const slug = e.target.value;
    form.setValue('slug', slug);

    // If the slug is empty, reset the manually edited flag
    // This allows auto-generation to resume if the user clears the field
    if (slug === '') {
      setSlugManuallyEdited(false);
      return;
    }

    // Mark that the user has manually edited the slug
    setSlugManuallyEdited(true);

    if (slug.length >= 2) {
      checkSlugAvailability(slug);
    } else {
      setSlugAvailable(null);
    }
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setPending(true);

    try {
      await organization.create({
        name: values.name,
        slug: values.slug,
      });

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess({
          name: values.name,
          slug: values.slug,
        });
      }
    } catch (error) {
      setPending(false);
      log.warn('create organization', { error });
      form.setError('root', { message: 'Failed to create organization' });

      // Call the error callback if provided
      if (onError) {
        onError(error);
      }
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Organization Name"
                  disabled={pending}
                  {...field}
                  onChange={onNameChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="slug"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="text"
                  placeholder="organization-slug"
                  disabled={pending}
                  {...field}
                  onChange={onSlugChange}
                />
              </FormControl>
              <FormMessage />
              {slugAvailable === true && (
                <p className="text-green-600 text-sm">Slug is available</p>
              )}
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full"
          disabled={pending || slugAvailable === false}
        >
          {pending ? (
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          Create Organization
        </Button>

        {form.formState.errors.root && (
          <p
            data-slot="form-message"
            className="text-destructive-foreground text-sm"
          >
            {form.formState.errors.root?.message}
          </p>
        )}
      </form>
    </Form>
  );
};
