import { betterAuth } from 'better-auth';
import { option } from './server';
import {
  getAuthServerCookieDomain,
  determineAuthServerType,
  getTrustedOriginsForAuthServer,
  type AuthServerType,
} from './domain-config';

/**
 * Create auth instance for a specific server type
 * Each server gets its own cookie domain configuration
 */
function createAuthInstance(
  serverType: AuthServerType,
  customizations: Record<string, unknown> = {}
) {
  return betterAuth({
    ...option,
    ...customizations,
    advanced: {
      ...option.advanced,
      crossSubDomainCookies: {
        enabled: process.env.NODE_ENV === 'production',
        domain: getAuthServerCookieDomain(serverType),
      },
    },
    trustedOrigins: getTrustedOriginsForAuthServer(serverType),
  });
}

/**
 * Organizer-specific auth instance
 * Used by app.ticketcare.my (organizer dashboard)
 * Cookies: app.ticketcare.my (NOT accessible to www.ticketcare.my)
 * API at api.ticketcare.my can read these cookies via broader domain
 */
export const organizerAuth = createAuthInstance('organizer', {
  // Keep email/password enabled for organizers
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  // Keep organization plugin for organizers
  plugins: option.plugins,
});

/**
 * Customer-specific auth instance
 * Used by www.ticketcare.my (customer portal)
 * Cookies: www.ticketcare.my (NOT accessible to app.ticketcare.my)
 * API at api.ticketcare.my can read these cookies via broader domain
 */
export const customerAuth = createAuthInstance('customer', {
  // Disable email/password for customers (magic link only)
  emailAndPassword: {
    enabled: false,
  },
  // Disable email verification on signup for customers
  emailVerification: {
    ...option.emailVerification,
    sendOnSignUp: false,
  },
  // Remove organization plugin for customers
  plugins: option.plugins?.filter((plugin: { id?: string }) => {
    return !plugin.id || plugin.id !== 'organization';
  }),
});

/**
 * API auth instance
 * Used by api.ticketcare.my
 * Can read cookies from both app.ticketcare.my and www.ticketcare.my
 * via broader .ticketcare.my domain
 */
export const apiAuth = createAuthInstance('api', {
  // API keeps all features enabled
  plugins: option.plugins,
});

/**
 * Auto-detect which auth instance to use based on current host
 * Useful for shared configuration files
 */
export function getAuthForCurrentHost(host?: string) {
  const serverType = determineAuthServerType(host);

  switch (serverType) {
    case 'organizer':
      return organizerAuth;
    case 'customer':
      return customerAuth;
    case 'api':
      return apiAuth;
    default:
      return apiAuth; // Fallback to API
  }
}

/**
 * Helper to validate session from multiple auth sources in API
 * Since API can receive requests from both organizer and customer apps
 */
export async function validateAnySession(request: Request) {
  // Try organizer auth first
  try {
    const orgSession = await organizerAuth.api.getSession({
      headers: request.headers,
    });
    if (orgSession) {
      return { session: orgSession, type: 'organizer' as const };
    }
  } catch (error) {
    // Organizer session not found or invalid
    console.error('Error validating organizer session:', error);
  }

  // Try customer auth
  try {
    const custSession = await customerAuth.api.getSession({
      headers: request.headers,
    });
    if (custSession) {
      return { session: custSession, type: 'customer' as const };
    }
  } catch (error) {
    // Customer session not found or invalid
    console.error('Error validating customer session:', error);
  }

  // No valid session found
  return null;
}

export type OrganizerAuth = typeof organizerAuth.$Infer.Session;
export type CustomerAuth = typeof customerAuth.$Infer.Session;
export type ApiAuth = typeof apiAuth.$Infer.Session;
