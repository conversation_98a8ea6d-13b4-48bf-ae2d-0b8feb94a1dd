/**
 * Organizer-specific auth client configuration
 * Used by app.ticketcare.my (organizer dashboard)
 */

import { organizerAuthClient } from './client';

// Re-export the organizer-specific client with proper typing
export const authClient = organizerAuthClient;

// Export individual methods for convenience
export const {
  signIn,
  signUp,
  signOut,
  useSession,
  admin,
  organization,
  useActiveOrganization,
  emailOtp,
} = organizerAuthClient;

export default organizerAuthClient;
